# Multi-Page Profile Collection Guide

## Overview
The LinkedIn extension now supports collecting profiles from multiple pages (1-4) automatically. This feature allows you to gather more profiles in a single operation without manually navigating through pages.

## How to Use

### Step 1: Navigate to LinkedIn Search
1. Open LinkedIn and perform a search for people
2. Apply your desired filters (location, industry, company, etc.)
3. Make sure you're on the search results page

### Step 2: Open the Extension
1. Click on the LinkedIn extension icon
2. Choose "Create Campaign"
3. Select "Add people from LinkedIn search"

### Step 3: Start Multi-Page Collection
1. You'll see three options:
   - **SHOW LINKEDIN FILTERS** - Opens LinkedIn search filters
   - **START COLLECTING PEOPLE** - Collects from current page only
   - **COLLECT PAGES 1-4** - NEW! Collects from pages 1 through 4 automatically

2. Click **"COLLECT PAGES 1-4"** to start the multi-page collection

### What Happens During Collection
- The extension will automatically navigate through pages 1-4
- It collects profiles from each page
- Profiles appear in real-time as they're collected
- Duplicates are automatically filtered out
- Each profile is tagged with its source page number

### Features
- **Automatic Navigation**: No manual page clicking required
- **Real-time Updates**: See profiles as they're collected
- **Duplicate Prevention**: Same profile won't be added twice
- **Page Tracking**: Each profile shows which page it came from
- **Error Handling**: Graceful handling of navigation issues

### Requirements
- Must be on a LinkedIn search results page
- LinkedIn must be loaded and accessible
- Extension must have proper permissions

### Tips
- Use specific search filters before starting collection for better results
- The process takes about 2-3 seconds per page
- You can see progress updates in the extension popup
- All collected profiles will be available for messaging campaigns

### Troubleshooting
- If collection fails, ensure you're on a LinkedIn search results page
- Refresh the LinkedIn page if navigation issues occur
- Check browser console for detailed error messages
- Make sure LinkedIn is fully loaded before starting collection

## Technical Details
- Collects from pages 1, 2, 3, and 4 sequentially
- Uses both pagination buttons and URL manipulation for navigation
- Implements delays to avoid rate limiting
- Sends real-time updates to the extension popup
- Maintains profile data integrity across pages
